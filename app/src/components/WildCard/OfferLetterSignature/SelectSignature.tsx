import React, { useEffect, useState } from "react";
import { FormGroup } from "react-bootstrap";
import SelectField from "@src/components/input/SelectField";

// Props for the EmployeeSignatureModal component
type SelectSignatureModalProps = {
  onAddSignature: (signatureData: any) => void;
  allSinature: any[];
  fetchEmployeesSignature: (search: string) => void;
  signature: any;
  setSignature: (signature: any) => void;
  setIsChangeSignature: (isChangeSignature: boolean) => void;
};

// Component for the modal to upload employee signature
const SelectSignature = ({
  onAddSignature,
  allSinature,
  fetchEmployeesSignature,
  signature,
  setSignature,
  setIsChangeSignature,
}: SelectSignatureModalProps) => {
  const [employeesSignature, setEmployeesSignature] = useState<Array<any>>([]);
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    setEmployeesSignature(allSinature);
  });

  const onEmployeeSignatureChange = (event: any) => {
    setSignature(event.target.value);
    setIsChangeSignature(true);
  };

  return (
    <>
      <FormGroup className="mb-3">
        <SelectField
          value={signature}
          name={"signature_id"}
          label={"Employee Signature"}
          placeholder={"Select Employee Signature"}
          showSearch={true}
          onClear={() => setSignature(null)}
          options={employeesSignature}
          onSearch={fetchEmployeesSignature}
          loading={loading}
          selectEmpty
          onChangeInput={onEmployeeSignatureChange}
        />
      </FormGroup>
    </>
  );
};

export default SelectSignature;
